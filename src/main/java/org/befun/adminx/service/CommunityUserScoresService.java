package org.befun.adminx.service;

import lombok.extern.slf4j.Slf4j;
import org.befun.adminx.constant.ScoreType;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.entity.Admin;
import org.befun.adminx.entity.CommunityUser;
import org.befun.adminx.entity.CommunityUserScores;
import org.befun.adminx.entity.CommunityUserScoresDto;
import org.befun.adminx.repository.CommunityUserScoresRepository;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;


/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommunityUserScoresService extends BaseService<CommunityUserScores, CommunityUserScoresDto, CommunityUserScoresRepository> {

    @Autowired
    CommunityUserScoresService communityUserScoresService;

    @Autowired
    CommunityUserScoresRepository communityUserScoresRepository;

    @Autowired
    private AdminService adminService;


    @Override
    public Page<CommunityUserScoresDto> findAll(ResourceEntityQueryDto<CommunityUserScoresDto> queryDto) {
        queryDto.setSorts(Sort.by("id").descending());
        if(queryDto.getQueryCriteriaList().size() > 0) {
            queryDto.getQueryCriteriaList().stream().forEach(query ->{
                if(query.getKey().equals("name")){
                    query.setOperator(QueryOperator.LIKE);
                }
                if(query.getKey().equals("operator")){
                    query.setOperator(QueryOperator.LIKE);
                }
            });
        }
        return super.findAll(queryDto);
    }


    /**
     * 增加积分变化记录
     * @param communityUser
     * @param score
     * @param type
     * @param survey
     */
    public void addScoreRecord(CommunityUser communityUser, Integer score, ScoreType type , SurveySimpleDto survey){
        addScoreRecord( communityUser,  score,  type ,  survey, null);
    }


    /**
     * 增加积分变化记录
     * @param communityUser
     * @param score
     * @param type
     * @param survey
     * @param message 发生错误时写入的消息
     */
    public void addScoreRecord(CommunityUser communityUser, Integer score, ScoreType type , SurveySimpleDto survey,String message){
        addScoreRecord(communityUser, score, type, survey, message, null);
    }

    /**
     * 增加积分变化记录
     * @param communityUser
     * @param score
     * @param type
     * @param survey
     * @param message 发生错误时写入的消息
     * @param shareId 分享用户ID
     */
    public void addScoreRecord(CommunityUser communityUser, Integer score, ScoreType type , SurveySimpleDto survey,String message, Long shareId){
        CommunityUserScores communityUserScores = setTitleAndType(score, type, survey);
//        communityUserScores.setCuid(communityUser.getId());
        communityUserScores.setUser(communityUser);
        communityUserScores.setScore(communityUser.getUserScore().longValue());
        communityUserScores.setMessage(message);
        communityUserScores.setShareId(shareId);
        Admin admin = adminService.get(TenantContext.getCurrentUserId());
        if(admin != null){
            communityUserScores.setOperator(admin.getUserName());
        }
        communityUserScoresRepository.save(communityUserScores);
    }

    public CommunityUserScores addScoreRecordNew(CommunityUser communityUser, Integer score, ScoreType type , SurveySimpleDto survey, String message){
        return addScoreRecordNew(communityUser, score, type, survey, message, null);
    }

    public CommunityUserScores addScoreRecordNew(CommunityUser communityUser, Integer score, ScoreType type , SurveySimpleDto survey, String message, Long shareId){
        if (communityUser == null) return null;
        CommunityUserScores communityUserScores = setTitleAndType(score, type, survey);
        communityUserScores.setUser(communityUser);
        communityUserScores.setScore(communityUser.getUserScore().longValue());
        communityUserScores.setMessage(message);
        communityUserScores.setShareId(shareId);
        return communityUserScores;
    }

    /**
     * 初始化积分变动的title和类型
     * @param score
     * @param type
     * @param survey
     * @return
     */
    private CommunityUserScores setTitleAndType(Integer score, ScoreType type, SurveySimpleDto survey) {
        CommunityUserScores userScores = new CommunityUserScores();
        Long scoreChange = 0l;
        switch (type){
            case R:
                userScores.setTitle(String.format("回答问卷【%s】奖励%d积分。", survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case U:
                userScores.setTitle(String.format("等级从【%s】奖励%d积分。", survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case A:
                userScores.setTitle(String.format("完善账号信息，增加%d积分。", score));
                scoreChange = score.longValue();
                break;
            case M:
                userScores.setTitle(String.format("积分兑换码【%s】兑换%d积分。", survey.getSurveyTitle(), score));
                scoreChange = -score.longValue();
                break;
            case F:
                userScores.setTitle(String.format("您填答的【%s】正在审核，通过后您将获得%d积分的答题奖励。", survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case F_P:
                userScores.setTitle(String.format("您通过了【%s】的审核，获得%d积分的答题奖励。", survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case F_N:
                userScores.setTitle(String.format("您填答的【%s】审核不通过，您无法获得该问卷的%d积分答题奖励。", survey.getSurveyTitle(), score));
                scoreChange = -score.longValue();
                break;
            case I_V:
                userScores.setTitle(String.format("您邀请的用户【%s】通过了【%s】的审核，您同步获得%d积分的佣金奖励。", survey.getNickName(), survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case G_V:
                userScores.setTitle(String.format("您邀请的用户【%s】通过了【%s】的审核，您获得%d积分的网格奖励。", survey.getNickName(), survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case G_F:
                userScores.setTitle(String.format("您邀请的用户【%s】未通过【%s】的审核，您未能获得%d积分的网格奖励。", survey.getNickName(), survey.getSurveyTitle(), score));
                scoreChange = -score.longValue();
                break;
            case I_A:
                userScores.setTitle(String.format("用户【%s】通过了【%s】的审核，您邀请新用户成功，获得%d积分的邀请奖励。", survey.getNickName(), survey.getSurveyTitle(), score));
                scoreChange = score.longValue();
                break;
            case D_C:
                userScores.setTitle("微信提现 -" + score.doubleValue()/100 + "元");
                scoreChange = -score.longValue();
                break;
            case D_F:
                userScores.setTitle("微信提现失败 -" + score.doubleValue()/100 + "元");
                scoreChange = score.longValue();
                break;
            case D_H:
                userScores.setTitle("提现成功 -" + score.doubleValue()/100 + "元");
                scoreChange = -score.longValue();
                break;
            case D_A:
                userScores.setTitle("增加成功 +" + score.doubleValue()/100 + "元");
                scoreChange = score.longValue();
                break;
            default:
        }
        userScores.setScoreChange(scoreChange);
        userScores.setType(type.getText());
        userScores.setSurveyId(survey.getSurveyId());
        userScores.setSurveyTitle(survey.getSurveyTitle());
        return userScores;
    }

    /**
     * 社区用户获得的总金额
     * @return
     */
    public Long sumUserScore() {
        return repository.sumUserScore();
    }

    public Long sumInviteScore(Long cuid) {
        return repository.sumScoreByCuidAndType(cuid, ScoreType.I_V.getText());
    }

}
