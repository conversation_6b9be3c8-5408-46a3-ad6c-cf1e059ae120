package org.befun.adminx.service;

import cn.hanyi.common.ip.resolver.IpResolverService;
import com.alibaba.excel.EasyExcel;
import com.github.binarywang.wxpay.bean.result.WxPaySendRedpackResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.befun.adminx.annotation.UserQueryField;
import org.befun.adminx.constant.*;
import org.befun.adminx.converter.EducationConverter;
import org.befun.adminx.converter.SexConverter;
import org.befun.adminx.dto.audit.AuditResponseDto;
import org.befun.adminx.dto.community.*;
import org.befun.adminx.dto.ext.SendFollowTaskDto;
import org.befun.adminx.dto.ext.SendTaskExDto;
import org.befun.adminx.dto.notify.VerifyCodeDto;
import org.befun.adminx.dto.query.CommunityUserExportQueryDto;
import org.befun.adminx.dto.query.CommunityUserQueryDto;
import org.befun.adminx.dto.query.CommunityUserSearchDto;
import org.befun.adminx.dto.sample.SimpleUserDto;
import org.befun.adminx.dto.sms.MessageSendResponseInfo;
import org.befun.adminx.dto.sms.SmsNotifyTextInfo;
import org.befun.adminx.dto.survey.SurveySimpleDto;
import org.befun.adminx.dto.task.TemplateInfoDto;
import org.befun.adminx.dto.user.ExportUserDto;
import org.befun.adminx.dto.user.UserStatisticsDto;
import org.befun.adminx.dto.wechat.WechatAuthorizeDto;
import org.befun.adminx.dto.wechat.WechatNotifyRequestDto;
import org.befun.adminx.dto.wechat.WechatResponseDto;
import org.befun.adminx.entity.*;
import org.befun.adminx.entity.dto.Additional;
import org.befun.adminx.entity.survey.SurveyTrackingDataDto;
import org.befun.adminx.exception.SurveyErrorCode;
import org.befun.adminx.exception.SurveyErrorException;
import org.befun.adminx.repository.*;
import org.befun.adminx.service.audit.AuditContent;
import org.befun.adminx.service.wechat.WechatConfigureService;
import org.befun.adminx.service.wechat.WechatPayService;
import org.befun.adminx.service.wechat.WechatSubscribeService;
import org.befun.adminx.task.sms.SmsService;
import org.befun.adminx.utils.DateFormatter;
import org.befun.adminx.utils.RegularExpressionUtils;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommunityUserService extends BaseService<CommunityUser, CommunityUserDto, CommunityUserRepository> {

    @Autowired
    private CommunityUserRepository communityUserRepository;

    @Autowired
    private CommunityUserScoresService communityUserScoresService;

    @Autowired
    private SurveyCompletedRecordService surveyCompletedRecordService;

    @Autowired
    private WechatConfigureService wechatConfigureService;

    @Autowired
    private CommunityUserSessionService sessionService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private ReplyMessageService replyMessageService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private WechatSubscribeService wechatSubscribeService;

    @Autowired
    private WechatPayService wechatPayService;

    @Autowired
    private CommunityUserAdditionalRepository additionalRepository;

    @Autowired
    private IpResolverService ipResolverService;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private InviteAwardService inviteAwardService;

    @Value("${community.complete-user-info-score}")
    private Integer completeUserInfoScore;

    @Value("${befun.admin.invite-url}")
    private String xmPlusUrl;

    @Value("${community.invite-score-percent}")
    private Double inviteScorePercent;

    @Value("${community.invite-score-max}")
    private Double inviteScoreMax;

    @Autowired
    protected SmsService smsService;

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private FeatureRepository featureRepository;

    @Autowired
    private CommunityGridRepository communityGridRepository;

    @Autowired
    private SurveyCompletedRecordRepository surveyCompletedRecordRepository;

    @SneakyThrows
    @Override
    public <S extends ResourceCustomQueryDto> Page<CommunityUserDto> findAll(S query) {
        CommunityUserQueryDto dto = (CommunityUserQueryDto) query;
        ResourceEntityQueryDto<CommunityUserDto> params = dto.transform();
        Field[] fields = dto.getClass().getDeclaredFields();
        List<String> additional = Arrays.asList("gender", "education", "birthdayLte", "birthdayGte", "additionalCreateTimeLte", "additionalCreateTimeGte");
        Map<String, Object> queryParamsMap = new HashMap<>();

        Arrays.stream(fields).filter(field -> field.getAnnotation(UserQueryField.class) != null).forEach(field -> {
            try {
                String name = field.getName();
                // 将属性的首字符大写，方便构造get，set方法
                Method m = dto.getClass().getMethod("get" + name.substring(0,1).toUpperCase() + name.substring(1));
                if(m.invoke(dto) != null){
                    queryParamsMap.put(name, m.invoke(dto));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        List<UserStatisticsDto> result = statisticsService.searchUser(queryParamsMap);
        if(result != null && result.size() >0) {
            List<Integer> ids = new ArrayList<>(result.stream().map(u -> u.getCuid()).collect(Collectors.toList()));
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("id", ids, QueryOperator.IN));
        }else if(result != null && result.size() == 0){
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("id", new ArrayList<>(), QueryOperator.IN));
        }
        Arrays.stream(fields).filter(field -> field.getAnnotation(UserQueryField.class) == null).forEach(field -> {
            QueryOperator operator = QueryOperator.EQUAL;
            String name = field.getName();
            String key = name;
            if(name.indexOf("Gte") != -1){
                key = name.substring(0,key.indexOf("Gte"));
                operator = QueryOperator.GREATER_THAN_EQUAL;
            }else if(name.indexOf("Lte") != -1) {
                key = name.substring(0,key.indexOf("Lte"));
                operator = QueryOperator.LESS_THAN_EQUAL;
            }
            if(additional.contains(name)){
                key = "additional." + key.replace("additionalCreateTime","createTime");
            }

            try {
                //昵称模糊查询
                if(name.equals("nickName")) operator = QueryOperator.LIKE;
                // 将属性的首字符大写，方便构造get，set方法
                name = name.substring(0,1).toUpperCase() + name.substring(1);
                Method m = dto.getClass().getMethod("get" + name);
                Object value = m.invoke(dto);
                if(value != null) {
                    if(name.equals("WechatSubscribeTimeLte") || name.equals("WechatSubscribeTimeGte") || name.equals("WechatUnSubscribeTimeLte") || name.equals("WechatUnSubscribeTimeGte")) {
                        value = ((Date) value).getTime() / 1000;
                        params.getQueryCriteriaList().add(new ResourceQueryCriteria(key, value, operator));
                    } else if("ProvinceIn".equals(name)) {//位置搜索
                        List<String> provinceList = Arrays.asList(((String) value).split(","));
                        params.getQueryCriteriaList().add(new ResourceQueryCriteria("additional.province",
                                provinceList.stream().distinct().collect(Collectors.toList()), QueryOperator.IN));
                    } else if("CityIn".equals(name)) {//位置搜索
                        List<String> cityList = Arrays.asList(((String) value).split(","));
                        params.getQueryCriteriaList().add(new ResourceQueryCriteria("additional.city",
                                cityList.stream().distinct().collect(Collectors.toList()), QueryOperator.IN));
                    } else {
                        params.getQueryCriteriaList().add(new ResourceQueryCriteria(key, value, operator));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return findAll(params);
    }

    @Override
    protected void afterMapToDto(List<CommunityUser> entity, List<CommunityUserDto> dto) {
        super.afterMapToDto(entity, dto);
        dto.forEach(user -> {
            if (user.getAdditional() == null) {
                user.setAdditional(new CommunityUserAdditional());
            } else {
                user.setProvince(user.getAdditional().getProvince());
                user.setCity(user.getAdditional().getCity());
                user.setArea(user.getAdditional().getArea());
            }
            user.setStatistics(statisticsService.getUserStatisticsData(user.getId()));
            CommunityGrid grid = communityGridRepository.findFirstByGridId(user.getGridId());
            if (grid != null) {
                user.setGridName(grid.getGridName());
            }
        });
    }

    private Map<String, Object> getGridMap(List<Long> ids) {
        List<CommunityGrid> communityGrids = communityGridRepository.findAllById(ids);
        return communityGrids.stream().collect(Collectors.toMap(CommunityGrid::getGridId, Function.identity()));
    }

    @Override
    public CommunityUserDto mapToDto(CommunityUser entity) {
        CommunityUserDto dto = super.mapToDto(entity);
        dto.setInviteUrl(String.format(xmPlusUrl, dto.getUserName()));
        //如果
        if (dto.getSurveyCount() == null) {
            Long count = surveyCompletedRecordService.countCompleteSurveys(dto.getOpenId());
            dto.setSurveyCount(count);
            entity.setSurveyCount(count);
            repository.save(entity);
        }
        if(dto.getAdditional() != null) {
            dto.setProvince(entity.getAdditional().getProvince());
            dto.setCity(entity.getAdditional().getCity());
            dto.setArea(entity.getAdditional().getArea());
            Optional.ofNullable(dto.getAdditional().getGender()).ifPresent(g ->
                    dto.getAdditional().setGender(new SexConverter().convertToString(dto.getAdditional().getGender())));
            Optional.ofNullable(dto.getAdditional().getEducation()).ifPresent(g ->
                    dto.getAdditional().setEducation(new EducationConverter().convertToString(dto.getAdditional().getEducation())));
        }
        return dto;
    }

    public AuditResponseDto block(CommunityUser user) {
        user.setStatus(2);
        repository.save(user);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    public AuditResponseDto unblock(CommunityUser user) {
        user.setStatus(1);
        repository.save(user);
        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * *
     * @param dto
     * @return
     */
    public AuditResponseDto batchBlock(BlockUserDto dto) {
        if(dto == null || StringUtils.isEmpty(dto.getCuidList())) {
            throw new BadRequestException("cuid列表不能为空");
        }
        if(!List.of(1,2).contains(dto.getStatus())) {
            throw new BadRequestException("该功能只支持禁用和启用用户");
        }

        //过滤中英文逗号 "," "，"
        String cuidContent = dto.getCuidList().replaceAll("\n",",").replaceAll("，",",");

        Collection<Long> ids = new ArrayList<>();

        for (String str : cuidContent.split(",")) {
            try {
                ids.add(Long.parseLong(str.trim()));
            } catch (NumberFormatException e) {
                throw new BadRequestException("输入的cuid包含非数字字符");
            }
        }

        //批量修改
        communityUserRepository.updateStatusByIdIn(dto.getStatus(), ids);

        return AuditResponseDto.builder()
                .message("成功")
                .build();
    }

    /**
     * 审核通过给用户增加积分
     * @param openId
     * @param type
     * @param taskDto
     */
    public CommunityUser addUserScore(String openId, ScoreType type, DeliveryTaskDto taskDto){
        if(openId.isEmpty()){
            return null;
        }
        Integer score = taskDto.getScore();
        if(score.intValue() < 0){
            return null;
        }
        try {
            Optional<CommunityUser> optionalCommunityUser = communityUserRepository.findByOpenId(openId);
            if(optionalCommunityUser.isPresent()){
                CommunityUser communityUser = optionalCommunityUser.get();
                switch (type) {
                    case F:
                        communityUser.setFreezeScore(communityUser.getFreezeScore() + score);
                        break;
                    case F_P:
                        //审核问卷 需要增加可用积分和总积分 减少冻结积分
                        communityUser.setNeedSurveys(communityUser.getNeedSurveys() - 1);
                        communityUser.setSurveysCompletedCount(communityUser.getSurveysCompletedCount() + 1);
                        communityUser.setUserScore(communityUser.getUserScore().intValue() + score);
                        communityUser.setAccumulativeScore(communityUser.getAccumulativeScore().intValue() + score);
                        communityUser.setFreezeScore(communityUser.getFreezeScore() - score >0 ? communityUser.getFreezeScore() - score : 0);
                        communityUser = levelUp(communityUser);
                        break;
                    case F_N:
                        communityUser.setFreezeScore(communityUser.getFreezeScore() - score >0 ? communityUser.getFreezeScore() - score : 0);
                        break;
                    default:
                }
                communityUserRepository.save(communityUser);
                //填答问卷只需要写入积分记录
                communityUserScoresService.addScoreRecord(communityUser, score, type, new SurveySimpleDto(taskDto.getSid(), taskDto.getName(), communityUser.getNickName()));
                return communityUser;
            }else {
                return null;
            }
        }catch (Exception e){
            throw new BadRequestException(e.getMessage());
        }
    }
    /**
     * 审核通过给用户增加积分
     * @param auditContent
     * @param type
     */
    @Transactional
    public void addUserScoreNew(AuditContent auditContent, ScoreType type){
        if(auditContent.getUserInfo() == null || auditContent.getDeliveryTask() == null) return;
        Integer score = auditContent.getDeliveryTask().getScore();
        if(score < 0) return;
        try {
            switch (type) {
                case F:
                    auditContent.getUserInfo().setFreezeScore(auditContent.getUserInfo().getFreezeScore() + score);
                    break;
                case F_P:
                    //审核问卷 需要增加可用积分和总积分 减少冻结积分
                    auditContent.getUserInfo().setNeedSurveys(auditContent.getUserInfo().getNeedSurveys() - 1);
                    auditContent.getUserInfo().setSurveysCompletedCount(auditContent.getUserInfo().getSurveysCompletedCount() + 1);
                    auditContent.getUserInfo().setUserScore(auditContent.getUserInfo().getUserScore() + score);
                    auditContent.getUserInfo().setAccumulativeScore(auditContent.getUserInfo().getAccumulativeScore() + score);
                    auditContent.getUserInfo().setFreezeScore(Math.max(auditContent.getUserInfo().getFreezeScore() - score, 0));
                    levelUpNew(auditContent);
                    break;
                case F_N:
                    auditContent.getUserInfo().setFreezeScore(Math.max(auditContent.getUserInfo().getFreezeScore() - score, 0));
                    break;
                default:
            }
            //填答问卷只需要写入积分记录
            auditContent.getUserScoresList().add(communityUserScoresService.addScoreRecordNew(auditContent.getUserInfo(), score, type, new SurveySimpleDto(auditContent.getSurveyId().toString(), auditContent.getDeliveryTask().getName(), auditContent.getUserInfo().getNickName()), null));
        }catch (Exception e){
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 给邀请人加转介绍费用和10%的奖励*
     * @param user
     * @param taskDto
     */
    public void addInviteAward(CommunityUser user, DeliveryTaskDto taskDto) {
        if(user == null || user.getInviteId() == null || user.getInviteId() == 0 || taskDto.getInviteScore() == null){
            return;
        }
        CommunityUser inviteUser = requireUser(user.getInviteId());
        if(inviteUser == null) return;

        int score = 0;
        ScoreType type;
        if(user.getSurveyCount() == null) {
            Long count = surveyCompletedRecordService.countCompleteSurveys(user.getOpenId());
            user.setSurveyCount(count);
        }
        if((null == user.getInviteAward() || !user.getInviteAward()) && taskDto.getEnableInvite() && user.getSurveyCount() != null && user.getSurveyCount() <= 1) {
            score = taskDto.getInviteScore();
            type = ScoreType.I_A;
            //如果没有关注 获取取关 转介绍费 7天内有效 7天内关注的话 邀请人可以获得奖励
            if(user.getWechatSubscribe() != WechatSubscribeStatus.SUBSCRIBE) {
                //写入未领取转介绍费记录
                inviteAwardService.insertInviteAward(user.getId(), inviteUser.getId(), score, taskDto.getId(), type);
                return;
            }
            user.setInviteAward(true);
            communityUserRepository.save(user);
        } else {
            if(taskDto.getScore() == null || taskDto.getScore() == 0){
                return;
            }
            score = limitInviteMaxScore(inviteUser.getId()) ? 0 : (int) Math.floor(taskDto.getScore() * inviteScorePercent);
            type = ScoreType.I_V;
        }

        //如果转介绍费没领取过 给用户转介绍费
        inviteUser.setUserScore(inviteUser.getUserScore().intValue() + score);
        inviteUser.setAccumulativeScore(inviteUser.getAccumulativeScore().intValue() + score);
        communityUserRepository.save(inviteUser);

        //填答问卷只需要写入积分记录
        communityUserScoresService.addScoreRecord(inviteUser, score, type, new SurveySimpleDto(taskDto.getSid(), taskDto.getName(), user.getNickName()));
    }

    private Boolean limitInviteMaxScore(Long inviteId) {
        Long score = communityUserScoresService.sumInviteScore(inviteId);
        log.info("inviteId: {}, score: {} ,max:{}", inviteId, score, inviteScoreMax);
        return score > inviteScoreMax;
    }

    /**
     * 给邀请人加转介绍费用和10%的奖励*
     * @param auditContent
     */
    public void addInviteAwardNew(AuditContent auditContent) {

        //存在grid，表示通过网格员催答填答
        if (auditContent.getSurveyCompletedRecord() != null && auditContent.getSurveyCompletedRecord().getGridId() != null){
            CommunityGrid grid = communityGridRepository.findFirstByGridId(auditContent.getSurveyCompletedRecord().getGridId());
            auditContent.setInviteUser(requireUser(grid.getCuid()));
        } else {
            auditContent.setInviteUser(requireUser(auditContent.getUserInfo().getInviteId()));
        }
        if(auditContent.getInviteUser() == null) return;

        int score = 0;
        ScoreType type;
        if(auditContent.getUserInfo().getSurveyCount() == null) {
            Long count = surveyCompletedRecordService.countCompleteSurveys(auditContent.getOpenId());
            auditContent.getUserInfo().setSurveyCount(count);
        }
        if((null == auditContent.getUserInfo().getInviteAward() || !auditContent.getUserInfo().getInviteAward()) && auditContent.getDeliveryTask().getEnableInvite() && auditContent.getUserInfo().getSurveyCount() != null && auditContent.getUserInfo().getSurveyCount() <= 1) {
            score = auditContent.getDeliveryTask().getInviteScore();
            type = ScoreType.I_A;
            //如果没有关注 获取取关 转介绍费 7天内有效 7天内关注的话 邀请人可以获得奖励
            if(auditContent.getUserInfo().getWechatSubscribe() != WechatSubscribeStatus.SUBSCRIBE) {
                //写入未领取转介绍费记录
                inviteAwardService.insertInviteAward(auditContent.getUserInfo().getId(), auditContent.getInviteUser().getId(), score, auditContent.getDeliveryTask().getId(), type);
                return;
            }
            auditContent.getUserInfo().setInviteAward(true);
        } else {
            if(auditContent.getDeliveryTask().getScore() == null || auditContent.getDeliveryTask().getScore() == 0){
                return;
            }
            score = limitInviteMaxScore(auditContent.getInviteUser().getId()) ? 0 : (int) Math.floor(auditContent.getDeliveryTask().getScore() * inviteScorePercent);
            type = ScoreType.I_V;
        }

        //如果转介绍费没领取过 给用户转介绍费
        auditContent.getInviteUser().setUserScore(auditContent.getInviteUser().getUserScore().intValue() + score);
        auditContent.getInviteUser().setAccumulativeScore(auditContent.getInviteUser().getAccumulativeScore().intValue() + score);

        //填答问卷只需要写入积分记录
        auditContent.getUserScoresList().add(communityUserScoresService.addScoreRecordNew(auditContent.getInviteUser(), score, type, new SurveySimpleDto(auditContent.getSurveyId().toString(), auditContent.getDeliveryTask().getName(), auditContent.getUserInfo().getNickName()), null));

    }

    public CommunityUser requireUser(Long cuid) {
        Optional<CommunityUser> communityUserOptional = repository.findById(cuid);
        CommunityUser communityUser = communityUserOptional.orElse(null);

        if (communityUser == null) {
            return null;
        }
        return communityUser;
    }

    public CommunityUser requireUser(String openId) {
        Optional<CommunityUser> communityUserOptional = repository.findByOpenId(openId);
        CommunityUser communityUser = communityUserOptional.orElse(null);
        return communityUser;
    }

    /**
     * 定时统计社区用户总数
     * @return
     */
    @Scheduled(fixedRate = 3600000)
//    @Scheduled(cron = "0 0 6 * * ?")
    public Long setCountUsers() {
        System.out.println("CountUser任务执行时间：" + LocalDateTime.now());
        Long count = repository.count();
        redisService.setCountUsers(count);
        return count;
    }

    /**
     * 定时统计用户总积分
     * @return
     */
//    @Scheduled(cron = "0 0 6 * * ?")
    @Scheduled(fixedRate = 3600000)
    public Long setCountScores() {
        System.out.println("CountScore任务执行时间：" + LocalDateTime.now());
        Long countScores = communityUserScoresService.sumUserScore();
        redisService.setCountScores((Long)(countScores/100));
        return (Long)(countScores/100);
    }

    /**
     * 获取用户总积分
     * @return
     */
    public Long getCountScores() {
        return redisService.getCountScores();
    }

    /**
     * 获取社区用户总数
     * @return
     */
    public Long getCountUsers() {
        return redisService.getCountUsers();
    }

    /**
     * 公众号用户获取详情
     * @param cuid
     * @return
     */
    public CommunityUserDto getUserAccount(Long cuid,String gridId) {
        CommunityUserDto communityUserDto = findOne(cuid);
        checkUserStatus(communityUserDto);

        CommunityGrid grid = communityGridRepository.findFirstByCuid(cuid);
        //当前用户是网格员
        if (grid != null) {
            communityUserDto.setIsGrid(true);
        }
        if(StringUtils.isEmpty(communityUserDto.getGridId())) {
            if(communityUserDto.getIsGrid() != null && communityUserDto.getIsGrid()){
                //自己是网格员且所属网格为空，设置所属网格为自己管理的网格
                communityUserDto.setGridId(grid.getGridId());
            }else {
                //不是网格员，未加入网格，加入网格gridId
                communityUserDto.setGridId(gridId);
            }
            communityUserRepository.findById(cuid).ifPresent(x->{
                x.setGridId(communityUserDto.getGridId());
                x.setJoinGridTime(new Date());
                communityUserRepository.save(x);
            });
        }

        Date educationModify = communityUserDto.getAdditional().getEducationModified();
        communityUserDto.setEducationModifiedOne(modifyOneYear(educationModify));
        //获取会员总人数
        communityUserDto.setCountOfUsers(getCountUsers());
        communityUserDto.setSumOfReward(getCountScores());
        return communityUserDto;
    }

    /**
     * 用户状态判断
     * @param user
     */
    public void checkUserStatus(CommunityUserDto user) {
        if(user == null)
            throw new BadRequestException("用户不存在");
        if(user.getStatus() == 2)
            throw new BadRequestException("用户已被拉黑");
    }

    /**
     * 是否一年内修改
     * @return
     */

    private Boolean modifyOneYear(Date educationModify){
        if(educationModify == null){
            return false;
        }
        Date date = DateUtils.addYears(educationModify, 1);
         if(new Date().compareTo(date)>0){
            return false;
         }
         return true;
    }

    /**
     * * 修改用户个人资料
     * @param cuid
     * @param additionalDto
     * @return
     */
    public CommunityUserDto setUserInfo(HttpServletRequest request,Long cuid, UserAdditionalDto additionalDto) {

        checkCity(request,additionalDto);

        CommunityUser communityUser = requireUser(cuid);
        if(communityUser == null || additionalDto == null) return mapToDto(communityUser);

        CommunityUserAdditional additional = communityUser.getAdditional() == null ? new CommunityUserAdditional() : communityUser.getAdditional();

        if(StringUtils.isNotEmpty(additionalDto.getSex())) additional.setGender(additionalDto.getSex());
        if(StringUtils.isNotEmpty(additionalDto.getEducation())) {
            log.info("cuid = {}, education = {}, education_code = {}",cuid, additionalDto.getEducation(), additionalDto.getEducation());
            additional.setEducation(additionalDto.getEducation());
            additional.setEducationModified(new Date());
        }

        if(additionalDto.getBirthday() != null) additional.setBirthday(additionalDto.getBirthday());
        additional.setId(cuid);
        //解析经纬度
        resolverLocation(additional,additionalDto.getLatitude(), additionalDto.getLongitude());

        additionalRepository.save(additional);
        //完善资料
        if(!"Y".equals(communityUser.getPersonalInfoCompleted())) {
            communityUser.setPersonalInfoCompleted("Y");
            communityUser.setUserScore(communityUser.getUserScore().intValue() + completeUserInfoScore);
            //写入积分记录
            communityUserScoresService.addScoreRecord(communityUser, completeUserInfoScore, ScoreType.A, new SurveySimpleDto());
        }
        if(StringUtils.isEmpty(communityUser.getUserName())) {
            communityUser.setUserName(getUserName(communityUser.getId()));
        }
        repository.save(communityUser);
        communityUser.setAdditional(additional);
        return mapToDto(communityUser);
    }

    /**
     * * 更新gps位置信息
     * @param request
     * @param cuid
     * @param additionalDto
     * @return
     */
    public CommunityUserDto updateLocation(HttpServletRequest request, Long cuid, UserAdditionalDto additionalDto) {
        checkCity(request,additionalDto);

        CommunityUser communityUser = requireUser(cuid);
        if(communityUser == null || additionalDto == null) throw new BadRequestException("用户不存在");

        CommunityUserAdditional additional = communityUser.getAdditional() == null ? new CommunityUserAdditional() : communityUser.getAdditional();
        additional.setId(cuid);
        //解析经纬度
        resolverLocation(additional,additionalDto.getLatitude(), additionalDto.getLongitude());
        additionalRepository.save(additional);
        repository.save(communityUser);
        communityUser.setAdditional(additional);
        return mapToDto(communityUser);
    }

    /**
     * 检查常住地是否为当前ip定位位置
     * @param request
     * @param additionalDto
     */
    private void checkCity(HttpServletRequest request,UserAdditionalDto additionalDto){
        String ip= RestUtils.getClientIpAddress(request);
//        RegionInfo regionInfo = ipResolverService.resolveIpToRegion(ip);
//        log.info("ip地址：{}省{}市", regionInfo.getProvince(),regionInfo.getCity());

        //没有授权地理位置
        if(additionalDto.getLatitude() == null || additionalDto.getLongitude() == null){
            throw new BadRequestException("提交资料需要授权您的位置信息，授权成功后您将收到更多的合适问卷");
        }
//        if(StringUtils.isEmpty(additionalDto.getSex())){
//            throw new BadRequestException("请选择您的性别");
//        }
//        if(StringUtils.isEmpty(additionalDto.getEducation())){
//            throw new BadRequestException("请选择您的学历");
//        }
//        if(additionalDto.getBirthday() == null){
//            throw new BadRequestException("请选择您的出生日期");
//        }
    }

    private CommunityUserDto setDefaultValue(CommunityUserDto userDto) {
        if(StringUtils.isEmpty(userDto.getUserName())) {
            userDto.setUserName(getUserName(userDto.getId()));
            userDto.getEntity().setUserName(getUserName(userDto.getId()));
        }
        return userDto;
    }

    /**
     * 获取微信授权链接
     * @param params
     * @return
     */
    public String getWechatRedirectUrl(WechatAuthorizeDto params) {
        String redirectUrl = wechatConfigureService.buildQrConnectUrl(params.getUrl(), "snsapi_userinfo", params.getState());
        return redirectUrl;
    }

    /**
     * 获取微信分享参数
     * @param url
     * @return
     */
    public WxJsapiSignature getWxSignature(String url) {
        if(StringUtils.isEmpty(url))
            throw new BadRequestException("url不能为空");
        WxJsapiSignature jsapiSignature = wechatConfigureService.getWxSignature(url);
        return jsapiSignature;
    }

    /**
     * 通过code获取用户信息
     * @param params
     * @return
     */
    public CommunityUserDto getUserAccountByCode(WechatAuthorizeDto params) {
        if(params.getCode().isEmpty() || params.getCode() == null) {
            throw new BadRequestException("微信code不能为空");
        }
        WxOAuth2AccessToken accessToken = wechatConfigureService.getAccessToken(params.getCode());
        if(accessToken.getOpenId().isEmpty() || accessToken.getOpenId() == null) {
            throw new BadRequestException("微信授权失败");
        }

        //通过openid获取用户信息
        CommunityUser communityUser = requireUser(accessToken.getOpenId());
        //如果用户不存在 帮用户注册
        if(communityUser == null) {
            WxOAuth2UserInfo userInfo = wechatConfigureService.getWxUserInfo(accessToken);
            if(userInfo == null) throw new BadRequestException("微信获取用户信息失败");
            communityUser = new CommunityUser();
            if(StringUtils.isNotEmpty(userInfo.getNickname()) && "微信用户".equals(userInfo.getNickname())) return mapToDto(communityUser);
            Long cuid = getMaxCuid();
            communityUser.initWechatUser(cuid, getInviteCuid(params.getState()), userInfo, wechatSubscribeService.getLastSubscribeByOpenId(accessToken.getOpenId()));
            communityUser.setUserName(getUserName(cuid));
            communityUser.setGridId(params.getGridId());
            repository.save(communityUser);
            //如果是被邀请的用户 增加邀请人数
            addInviteCount(getInviteCuid(params.getState()));
        }

        CommunityUserDto communityUserDto = mapToDto(communityUser);
        checkUserStatus(communityUserDto);
        communityUserDto.setToken(sessionService.getUserToken(communityUser));
        return communityUserDto;
    }

    public Long getMaxCuid(){
        return repository.findFirstByOrderByIdDesc().get().getId() + 1;
    }

    public Long getInviteCuid(String state) {
        if(StringUtils.isEmpty(state)) return 0l;

        if (StringUtils.isNotEmpty(state) && state.length() > 0 && StringUtils.isNumeric(state)) {
            return Long.parseLong(state);
        } else {
            Optional<CommunityUser> ops = repository.findFirstByUserNameOrderByIdDesc(state);
            if(ops.isPresent()) return ops.get().getId();
            else return 0l;
        }
    }
    /**
     * 用户升级
     * @param user
     * @return
     */
    public CommunityUser levelUp(CommunityUser user) {
        //升级需要的问卷数等于0时 给用户升级 并且增加 level*100 积分
        if(user.getNeedSurveys() == 0l && user.getLevel() < 10) {
            long level = user.getLevel() + 1;
            String title = String.format("%d级提升到%d级", user.getLevel(), level);
            user.setNeedSurveys(level+3);
            user.setLevel(level);
            user.setUserScore(user.getUserScore() + (int)level * 100);
            user.setAccumulativeScore(user.getAccumulativeScore() + (int)level * 100);
            user.setAwardScore((int)level * 100);
            communityUserRepository.save(user);
            //写入积分记录
            communityUserScoresService.addScoreRecord(user, (int)level * 100, ScoreType.U, new SurveySimpleDto(null, title, null));
        }
        return user;
    }
    /**
     * 用户升级
     * @param auditContent
     * @return
     */
    public void levelUpNew(AuditContent auditContent) {
        if (auditContent.getUserInfo() == null) return;
        //升级需要的问卷数等于0时 给用户升级 并且增加 level*100 积分
        if(auditContent.getUserInfo().getNeedSurveys() == 0 && auditContent.getUserInfo().getLevel() < 10) {
            long level = auditContent.getUserInfo().getLevel() + 1;
            String title = String.format("%d级提升到%d级", auditContent.getUserInfo().getLevel(), level);
            auditContent.getUserInfo().setNeedSurveys(level+3);
            auditContent.getUserInfo().setLevel(level);
            auditContent.getUserInfo().setUserScore(auditContent.getUserInfo().getUserScore() + (int)level * 100);
            auditContent.getUserInfo().setAccumulativeScore(auditContent.getUserInfo().getAccumulativeScore() + (int)level * 100);
            auditContent.getUserInfo().setAwardScore((int)level * 100);
            //写入积分记录
            auditContent.getUserScoresList().add(communityUserScoresService.addScoreRecordNew(auditContent.getUserInfo(), (int)level * 100, ScoreType.U, new SurveySimpleDto(null, title, null), null));
        }
    }

    private long getNeedSurveys(long level) {
        long need = 3;
        switch ((int) level) {
            case 1:
                need = 3;break;
            case 2:
                need = 7;break;
            case 3:
                need = 12;break;
            case 4:
                need = 18;break;
            case 5:
                need = 25;break;
            case 6:
                need = 33;break;
            case 7:
                need = 42;break;
            case 8:
                need = 52;break;
            case 9:
                need = 63;break;
            case 10:
                need = 75;break;
            default:
        }
        return need;
    }

    /**
     * 获取社区用户数据
     * @param communityUser
     * @return
     */
    public LinkedHashMap<String, Object> getCommunityContext(CommunityUser communityUser) {
        LinkedHashMap<String, Object> context = new LinkedHashMap<>();
        //社区用户信息
        LinkedHashMap<String, Object> communityUserMap = responseService.converterCommunityUser(communityUser);
        context.putAll(communityUserMap);
        return context;
    }

    /**
     * 关注、取关事件
     * @param requestDto
     * @return
     */
    public WechatResponseDto subscribeEvent(WechatNotifyRequestDto requestDto) {
        //用户openId
        String openId = requestDto.getFromUserName();
        // 事件类型，subscribe表示订阅，unsubscribe表示取消订阅, null表示用户发送消息过来
        String event = requestDto.getEvent();

        Boolean type;
        WechatSubscribeStatus status = WechatSubscribeStatus.UN_SUBSCRIBE;
        if (StringUtils.equals(event, "subscribe")) {
            log.info("用户关注了公众号,openId:{}",openId);
            type = true;
            status = WechatSubscribeStatus.SUBSCRIBE;
        } else if(StringUtils.equals(event, "unsubscribe")) {
            log.info("用户取消关注了公众号,openId:{}",openId);
            type = false;
            status = WechatSubscribeStatus.CANCEL_SUBSCRIBE;
        } else {
            return null;
        }
        CommunityUser user = requireUser(openId);
        if (user == null) {
            wechatSubscribeService.addRecord(null, openId, type);
        } else {
            wechatSubscribeService.addRecord(user.getId(), openId, type);
            user.setWechatSubscribe(status);
            //更新关注时间
            if(type) {
                user.setWechatSubscribeTime(requestDto.getCreateTime());
                inviteAwardService.addInviteAward(user);
            } else {
                user.setWechatUnSubscribeTime(requestDto.getCreateTime());
            }
            if(StringUtils.isEmpty(user.getUserName())) {
                user.setUserName(getUserName(user.getId()));
            }
            repository.save(user);
        }
        return WechatResponseDto.builder()
                .ToUserName(requestDto.getFromUserName())
                .FromUserName(requestDto.getToUserName())
                .CreateTime(new Date().getTime())
                .MsgType("text")
                .Content(replyMessageService.getReplyMessage("关注")).build();
    }

    //生成指定length的随机字符串（A-Z，a-z，0-9）
    private String getUserName(Long cuid) {
        return "W" + getRandomString(4) + cuid.toString();
    }
    /**
     * 生成指定length的随机字符串（A-Z，a-z，0-9）
     * 调研家users_name = "W" + 随机四位数字 + cuid
     * @param length
     * @return
     */
    private String getRandomString(int length) {
//        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        String str = "23456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(str.length());
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 搜索用户
     * @param sendTaskExDto
     * @return
     */
    public List<CommunityUserSearchDto> searchSendTaskUser(SendTaskExDto sendTaskExDto) {
        String sql = assemblySearchSql(sendTaskExDto);
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CommunityUserSearchDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    public List<CommunityUserSearchDto> searchSendFollowTaskUser(SendFollowTaskDto sendTaskExDto) {
        String sql = " select cu.cuid,cu.openid,cu.nickname,cu.tel as mobile,cu.wechat_subscribe from community_users cu " +
                " where cu.cuid in (" + sendTaskExDto.getCuidList().stream()
                .map(Object::toString)
                .collect(Collectors.joining(",")) + ")";
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CommunityUserSearchDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    public List<CommunityUserSearchDto> searchSendFollowTaskUser(List<Long> cuidList) {
        String sql = " select cu.cuid,cu.openid,cu.nickname,cu.tel as mobile,cu.wechat_subscribe from community_users cu " +
                " where cu.cuid in (" + cuidList.stream().map(Object::toString)
                .collect(Collectors.joining(",")) + ")";
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CommunityUserSearchDto.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }



    /**
     * 组装匹配用户sql
     * @param sendTaskExDto
     * @return
     */
    public String assemblySearchSql(SendTaskExDto sendTaskExDto) {
        StringBuilder sb = new StringBuilder(1024);
        StringBuilder statisticsInner = new StringBuilder(" ");
        sb.append(" select distinct cu.cuid,cu.openid,cu.nickname,cu.tel,cu.wechat_subscribe from community_users cu " +
                "inner join community_user_additional ca on ca.cuid = cu.cuid {STATISTICSINNER} where cu.wechat_subscribe = 1 ");

        if(!StringUtils.isEmpty(sendTaskExDto.getBlackList())) {
            sb.append(" and cu.cuid not in (" + sendTaskExDto.getBlackList() + ")");
        }

        if (sendTaskExDto.getSendType() == SendTaskType.RDS && !StringUtils.isEmpty(sendTaskExDto.getWhiteList())) {
            sb.append(" and cu.cuid in (" + sendTaskExDto.getWhiteList() + ")");
        }

        if(sendTaskExDto.getSendType() == SendTaskType.DESIGN && sendTaskExDto.getSamples().size() >0) {

            StringJoiner res = new StringJoiner("or");
            int resLength = res.length();
            sendTaskExDto.getSamples().forEach(samplesDto -> {
                StringJoiner joiner = new StringJoiner(" and ","(",")");
                int joinerLength = joiner.length();
                if(samplesDto.getGender() != null) {
                    joiner.add(String.format(" ca.sex = %s ", samplesDto.getGender()));
                }
                if(samplesDto.getAgeMin() != null) {
                    joiner.add(String.format(" TIMESTAMPDIFF(YEAR, ca.birthday, CURDATE()) >= '%s' ", samplesDto.getAgeMin()));
                }
                if(samplesDto.getAgeMax() != null) {
                    joiner.add(String.format(" TIMESTAMPDIFF(YEAR, ca.birthday, CURDATE()) <= '%s' ", samplesDto.getAgeMax()));
                }
                if(samplesDto.getEducations() != null && samplesDto.getEducations().size() >0) {
                    String education = StringUtils.strip(samplesDto.getEducations().stream().filter(e -> e != null).collect(Collectors.toList()).toString(), "[]");
                    joiner.add(String.format(" ca.education in (%s) ", education));
                }
                if(samplesDto.getLocations() != null && samplesDto.getLocations().size() >0) {
                    StringBuilder locationStr = new StringBuilder(1024);
                    samplesDto.getLocations().forEach(location ->{
                        if(location.size() ==1) {
                            locationStr.append(String.format(" ca.province = '%s' or", location.get(0)));
                        }else if(location.size() ==2) {
                            locationStr.append(String.format(" ca.city = '%s' or", location.get(1)));
                        }
                    });
                    joiner.add(" (" + StringUtils.strip(locationStr.toString(),"or") + ")");
                }

                if (samplesDto.getCommunityLevel() != null) {
                    Integer level = samplesDto.getCommunityLevel();
                    joiner.add(String.format(" cu.level = %s ", level));
                }
                if (StringUtils.isNotEmpty(samplesDto.getRegisterTimeStart())) {
                    joiner.add(String.format(" cu.create_time >= '%s' ", samplesDto.getRegisterTimeStart()));
                }
                if (StringUtils.isNotEmpty(samplesDto.getRegisterTimeEnd())) {
                    joiner.add(String.format(" cu.create_time <= '%s' ", samplesDto.getRegisterTimeEnd()));
                }

                if (StringUtils.isNotEmpty(samplesDto.getFollowTimeStart())) {
                    joiner.add(String.format(" FROM_UNIXTIME(cu.wechat_subscribe_time, '%%Y-%%m-%%d') >= '%s' ", samplesDto.getFollowTimeStart()));
                }
                if (StringUtils.isNotEmpty(samplesDto.getFollowTimeEnd())) {
                    joiner.add(String.format("FROM_UNIXTIME(cu.wechat_subscribe_time, '%%Y-%%m-%%d') <= '%s' ", samplesDto.getFollowTimeEnd()));
                }

                if (!samplesDto.completionIsNull() || !samplesDto.passIsNull() || !samplesDto.passRateIsNull()) {
                    List<String> sum = new ArrayList<>();
                    List<String> having = new ArrayList<>();
                    List.of(samplesDto.sumCaseCompleted(samplesDto.getCompletion()), samplesDto.sumCasePass(samplesDto.getPass()), samplesDto.sumCasePassRate(samplesDto.getPassRate())).forEach(s -> {
                        if (StringUtils.isNotEmpty(s.left) && StringUtils.isNotEmpty(s.right)) {
                            sum.add(s.left);
                            having.add(s.right);
                        }
                    });
                    String select = String.format(" INNER JOIN (SELECT cuid, %s FROM survey_statistics GROUP BY cuid HAVING %s) ss ON ss.cuid = cu.cuid", String.join(",", sum), String.join(" and ", having));
                    statisticsInner.append(select);
                }
                if (joiner.length() > joinerLength) {
                    res.add(joiner.toString());
                }
            });
            if (res.length() > resLength) {
                sb.append(" and (").append(res).append(") ");
            }
        }

        return sb.toString().replace("{STATISTICSINNER}", statisticsInner.toString());
    }

    /**
     * 获取用户填写的个人资料
     * @param cuid
     * @return
     */
    public Additional getUserAdditional(Long cuid) {
        String sql = String.format("select cuid,sex as gender,province, city,area,education,birthday " +
                " from community_user_additional " +
                " where cuid=%d;", cuid);
        try {
            return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(Additional.class));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * *批量获取社区用户信息
     * @param openIds
     * @return
     */
    public Map<String,CommunityUser> getUserByOpenIds(List<String> openIds) {
        if(openIds == null || openIds.size() == 0)  return null;
        List<CommunityUser> communityUserList = communityUserRepository.findByOpenIdIsIn(openIds);
        if(communityUserList == null || communityUserList.size() == 0) return null;
        Map<String,CommunityUser> map = new HashMap<>();
        communityUserList.stream().forEach(u -> {
            if(u != null) {
                map.put(u.getOpenId(),u);
            }
        });
        return map;
    }

    /**
     * *提现
     * @param request
     * @param cashDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public DrawCashResult drawCashNew(HttpServletRequest request, DrawCashDto cashDto) {

        Feature withdraw = featureRepository.findFirstByName("withdraw");
        if(Objects.isNull(withdraw) || !withdraw.getEnabled()){
            throw new BadRequestException("提现功能已关闭");
        }
        String key = String.format("befun.adminx.draw-cash.%s",cashDto.getOpenId());
        String value = UUID.randomUUID().toString();
        try {
            final Boolean success = redisTemplate.opsForValue().setIfAbsent(key, value, 30, TimeUnit.SECONDS);
            if(!success){
                throw new BadRequestException("系统繁忙");
            }
            return this.drawCash(request,cashDto);
        }catch (Exception e){
            log.error("提现错误：{}",e.getMessage());
           return new DrawCashResult(false,"提现失败");
        }finally {
            if(value.equals(redisTemplate.opsForValue().get(key))){
                redisTemplate.delete(key);
            }
        }
    }

    /**
     * *提现
     * @param request
     * @param cashDto
     * @return
     */
    @Transactional
    public DrawCashResult drawCash(HttpServletRequest request, DrawCashDto cashDto) {
        CommunityUser communityUser = requireUser(cashDto.getOpenId());
        checkUserStatus(mapToDto(communityUser));

        if(cashDto == null || cashDto.getAmount() == null || cashDto.getAmount() <= 0){
            return new DrawCashResult(false,"提现失败");
        }

        if(cashDto.getAmount() > communityUser.getUserScore())
            throw new BadRequestException("提现金额不能超过可用余额");

        ScoreType type = ScoreType.D_C;
        //扣除积分
        Integer balance = communityUser.getUserScore() - cashDto.getAmount();
        String message = null;
        try {
            WxPaySendRedpackResult result = wechatPayService.pay(request, cashDto.getOpenId(), cashDto.getAmount());
            String sendStatus = "SUCCESS";
            if (sendStatus.equals(result.getReturnCode()) && sendStatus.equals(result.getResultCode())){
                log.info("发送红包成功");
                //发送成功扣减积分
                type = ScoreType.D_C;
                communityUser.setUserScore(balance);
                repository.save(communityUser);
                return new DrawCashResult(true,"提现成功");
            }else {
                log.error("发送红包失败,{},{}",result.getReturnCode(),result.getReturnMsg());
                message = JsonHelper.toJson(result);
                type = ScoreType.D_F;
                return new DrawCashResult(result.getErrCode());
            }
        } catch (WxPayException e) {
            message = JsonHelper.toJson(e.getMessage());
            type = ScoreType.D_F;
            return new DrawCashResult(e.getErrCode());
        } catch (Exception e) {
            e.printStackTrace();
            message = JsonHelper.toJson(e.getMessage());
            type = ScoreType.D_F;
            return new DrawCashResult(false,"提现失败");
        } finally {
            //写入积分日志
            communityUserScoresService.addScoreRecord(communityUser, cashDto.getAmount(), type, new SurveySimpleDto(),message);
        }
    }

    /**
     * 后台积分提现，增加
     * @param cuid
     * @param cashDto
     * @return
     */
    @Transactional
    public CommunityUserDto handDrawCash(Long cuid, DrawCashDto cashDto) {

        if (cashDto.getAmount() == null && cashDto.getAddAmount() == null) {
            throw new BadRequestException("至少填写一个");
        }
        if ((cashDto.getAmount() != null && (cashDto.getAmount() < 0 || cashDto.getAmount() > 2000))) {
            throw new BadRequestException("只能输入0-2000以内的数值");
        }
        if ((cashDto.getAddAmount() != null && (cashDto.getAddAmount() < 0 || cashDto.getAddAmount() > 2000))) {
            throw new BadRequestException("只能输入0-2000以内的数值");
        }

        CommunityUser communityUser = requireUser(cuid);
        checkUserStatus(mapToDto(communityUser));

        if (cashDto.getAmount() != null && cashDto.getAmount() > communityUser.getUserScore()) {
            throw new BadRequestException("提现金额不能超过可用余额");
        }

        //扣除积分
        if (cashDto.getAmount() != null) {
            Integer balance = communityUser.getUserScore() - cashDto.getAmount();
            communityUser.setUserScore(balance);
            communityUserScoresService.addScoreRecord(communityUser, cashDto.getAmount(), ScoreType.D_H, new SurveySimpleDto());
        }
        if (cashDto.getAddAmount() != null) {
            communityUser.setUserScore(communityUser.getUserScore() + cashDto.getAddAmount());
            communityUserScoresService.addScoreRecord(communityUser, cashDto.getAddAmount(), ScoreType.D_A, new SurveySimpleDto());
        }

        repository.save(communityUser);
        return mapToDto(communityUser);
    }

    /**
     * 领取升级的奖励积分*
     * @param cuid
     * @return
     */
    public AuditResponseDto skipNotice(Long cuid, SkipNoticeDto dto) {
        CommunityUser communityUser = requireUser(cuid);
        if(communityUser == null) throw new BadRequestException("用户不存在");
        switch (dto.getType()) {
            case GET_AWARD:
                communityUser.setAwardScore(0);break;
            case AGREE_PROTOCOL:
            default:
        }
        Set<NoticeType> guideInfo = NoticeType.parse(communityUser.getGuideInfo());
        guideInfo.add(dto.getType());
        communityUser.setGuideInfo(NoticeType.format(guideInfo));
        repository.save(communityUser);
        return new AuditResponseDto();
    }

    /**
     * *通过openid批量获取用户信息
     * @param openidList
     * @return
     */
    public List<SimpleCommunityUserDto> getSimpleCommunityUserByOpenIds(List<String> openidList) {
        if(openidList == null || openidList.size() == 0) return List.of();
        return communityUserRepository.findByOpenIdIsInOrderByIdAsc(openidList);
    }

    /**
     * 更新用户地理位置
     * @param requestDto
     */
    public void updateUserLocation(WechatNotifyRequestDto requestDto) {
        String openid = requestDto.getFromUserName();
        Float latitude= requestDto.getLatitude();
        Float longitude = requestDto.getLongitude();
        Float precision = requestDto.getPrecision();
        if(StringUtils.isEmpty(openid) || latitude == null || longitude == null) return;

        CommunityUser user = requireUser(openid);
        if(user == null) return;
        resolverLocation(user.getAdditional(), latitude, longitude);
        additionalRepository.save(user.getAdditional());
    }

    /**
     * 根据经纬度定位用户所在的省市区
     * @param additional
     * @param latitude
     * @param longitude
     * @return
     */
    public void resolverLocation(CommunityUserAdditional additional, Float latitude, Float longitude) {
        if(additional == null) {
            additional = new CommunityUserAdditional();
        }
        SurveyTrackingDataDto trackingDataDto = trackingService.parseLocation(latitude,longitude);
        additional.setLatitude(latitude);
        additional.setLongitude(longitude);
        additional.setProvince(trackingDataDto.getProvince());
        additional.setCity(trackingDataDto.getCity());
        additional.setArea(trackingDataDto.getDistrict());
        additional.setGpsModified(new Date());
    }

    private void addInviteCount(Long inviteCuid) {
        if(inviteCuid == null || inviteCuid == 0l) return;
        CommunityUser inviteUser = requireUser(inviteCuid);
        if(inviteUser != null) {
            inviteUser.setNumberOfInvited(inviteUser.getNumberOfInvited() + 1);
            repository.save(inviteUser);
        }
    }

    /**
     * *获取邀请人列表
     * @param cuid
     * @return
     */
    public List<SimpleUserDto> getInviteList(Long cuid) {
        return repository.findByInviteIdOrderByIdDesc(cuid);
    }

    /**
     * 获取短信验证码
     * @param source
     * @param dto
     * @return
     */
    public AuditResponseDto getVerifyCode(String source, VerifyCodeDto dto) {
        if(dto == null || dto.getCuid() == null || StringUtils.isEmpty(dto.getMobile()))
            throw new BadRequestException("参数错误");
        CommunityUser user = requireUser(dto.getCuid());
        if(user == null) throw new BadRequestException("用户不存在");

        if(isBind(dto.getMobile()) && "bind".equals(source))
            throw new BadRequestException("该手机号已经绑定其他账号");
        String code = RandomStringUtils.randomNumeric(6);

        TemplateInfoDto template = thirdPartyTemplateService.getSmsTemplateByType(SendType.SEND_VERIFY_CODE);

        Map<String, Object> params = new HashMap<>();
        params.put("verifyCode",code);
        boolean result =  sendSms(dto.getMobile(), TemplateEngine.renderTextTemplate(template.getExample(), params));
        if(result) redisService.setVerifyCode(redisService.getVerifyCodeRedisKey(source,dto.getMobile()), code);
        return new AuditResponseDto();
    }

    /**
     * 绑定手机号
     * @param source
     * @param dto
     * @return
     */
    public CommunityUserDto bindMobile(String source, VerifyCodeDto dto) {
        if(dto == null || dto.getCuid() == null || StringUtils.isEmpty(dto.getMobile()) || StringUtils.isEmpty(dto.getCode()))
            throw new BadRequestException("参数错误");
        CommunityUser user = requireUser(dto.getCuid());
        if(user == null) throw new BadRequestException("用户不存在");
        if(isBind(dto.getMobile()))
            throw new BadRequestException("该手机号已经绑定其他账号");

        String key = redisService.getVerifyCodeRedisKey(source,dto.getMobile());
        if(redisService.verifyCodeFromRedis(key, dto.getCode())) {
            user.setMobile(dto.getMobile());
            repository.save(user);
            redisService.clearVerifyCode(key);
        }
        return mapToDto(user);
    }

    /**
     * 发送短信
     */
    public boolean sendSms(String mobile, String content) {
        if (!RegularExpressionUtils.isMobile(mobile)) throw new BadRequestException("手机号不正确");
        SmsNotifyTextInfo message = new SmsNotifyTextInfo();
        message.setContent(content);
        message.setMobile(mobile);
        try {
            MessageSendResponseInfo info = smsService.sendMessage3(message);
            if (info.isSuccess()) {
                log.info("短信验证码发送成功, mobile={}, content={}",  mobile, content);
                return true;
            } else {
                log.info("短信验证码发送失败, mobile={}, content={}",  mobile, content);
            }
        } catch (Exception e) {
            log.error("短信验证码发送失败, mobile={}, content={}, e={}", mobile, content, e.getMessage());
        }
        return false;
    }

    /**
     * 手机号是否已经绑定
     * @param mobile
     * @return
     */
    public boolean isBind(String mobile) {
        Optional<SimpleUserDto> optional = repository.findFirstByMobile(mobile);
        if(optional.isPresent()) return true;
        else return false;
    }

    public AuditResponseDto importUserInfoByCsv(List<UserInfoImportDto> userInfoImportDtoList) {
        Optional.ofNullable(userInfoImportDtoList).ifPresent(list -> {
            List<Long> cuidList = list.stream().map(UserInfoImportDto::getCuid).collect(Collectors.toList());
            List<CommunityUser> userList = repository.findByIdIn(cuidList);

            Optional.ofNullable(userList).ifPresent(l -> {
                Map<Long,CommunityUser> userMap = l.stream().collect(Collectors.toMap(CommunityUser::getId, user -> user));
                List<CommunityUser> userSaveList = new ArrayList<>();
                List<CommunityUserAdditional> additionalSaveList = new ArrayList<>();
                for (UserInfoImportDto entry : list) {
                    if(!userMap.containsKey(entry.getCuid())) continue;
                    CommunityUser user = userMap.get(entry.getCuid());
                    //如果没有手机号 设置手机号
                    if(StringUtils.isEmpty(user.getMobile()) && StringUtils.isNotEmpty(entry.getMobile())) {
                        user.setMobile(entry.getMobile());
                    }
                    //如果个人资料 没填写
                    additionalSaveList.add(this.updateAddition(user,entry));

                    user.setPersonalInfoCompleted("Y");
                    userSaveList.add(user);
                }
                repository.saveAll(userSaveList);
                additionalRepository.saveAll(additionalSaveList);
            });
        });
        return new AuditResponseDto();
    }

    @SneakyThrows
    public void exportUserInfo(HttpServletResponse response, CommunityUserExportQueryDto dto)  {

        String sql = "select * from (SELECT\n" +
                " u.cuid,\n" +
                " u.openid,\n" +
                " u.nickname wechatNickname,\n" +
                " u.tel,\n" +
                " u.grid_id gridId,\n" +
                " if(u.grid_id is null,null,(SELECT grid_name from community_grid where grid_id=CONVERT(u.grid_id USING utf8) COLLATE utf8_unicode_ci )) gridName,\n" +
                " u.accumulative_score accumulativeScore,\n" +
                " u.freeze_score freezeScore,\n" +
                " u.wechat_subscribe wechatSubscribe,\n" +
                " FROM_UNIXTIME(u.wechat_subscribe_time, '%Y-%m-%d') wechatSubscribeTime,\n" +
                " FROM_UNIXTIME(u.wechat_unsubscribe_time, '%Y-%m-%d') wechatUnsubscribeTime,\n" +
                " u.`level`,\n" +
                " u.number_of_invited numberOfInvited,\n" +
                " ua.sex,\n" +
                " ua.birthday,\n" +
                " ua.education,\n" +
                " ua.province,\n" +
                " ua.city,\n" +
                " ua.area,\n" +
                " u.create_time createTime,\n" +
                " ua.education_modified educationModified,\n" +
                " ua.gps_modified gpsModified,\n" +
                " ua.create_time additionalCreateTime,\n" +
                " s.total,\n" +
                " s.earlyCompleted,\n" +
                " s.quotaFull,\n" +
                " s.completed,\n" +
                " s.completedRate,\n" +
                " s.auditPass,\n" +
                " s.passRate \n" +
                " FROM\n" +
                " community_users u\n" +
                " LEFT JOIN community_user_additional ua ON u.cuid = ua.cuid \n" +
                " LEFT JOIN (\n" +
                " SELECT\n" +
                " cuid,\n" +
                " ifnull( sum( total ), 0 ) AS total,\n" +
                " ifnull( sum( completed ), 0 ) AS completed,\n" +
                " ifnull( sum( quota_full ), 0 ) AS quotaFull,\n" +
                " ifnull( sum( audit_pass ), 0 ) AS auditPass,\n" +
                " ifnull( sum( early_completed ), 0 ) AS earlyCompleted,\n" +
                " ifnull( sum( completed ) / sum( total ) * 100, 0 ) AS completedRate,\n" +
                " ifnull( sum( audit_pass ) / sum( completed ) * 100, 0 ) AS passRate \n" +
                " FROM\n" +
                " survey_statistics \n" +
                " GROUP BY cuid ) s ON u.cuid = s.cuid) t " +
                "%s limit 20000";

        String exeSql = String.format(sql,getCondition(dto));
        List<ExportUserDto> query = jdbcTemplate.query(exeSql, new BeanPropertyRowMapper<>(ExportUserDto.class));

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("社区用户资料", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;fileName=" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(),ExportUserDto.class).sheet("用户资料").doWrite(query);

    }

    private String getCondition(CommunityUserExportQueryDto dto){
        if(dto == null){
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder("where 1=1 ");
        Field[] declaredFields = dto.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = null;
            try {
                fieldValue = field.get(dto);
                if(fieldValue == null){
                    continue;
                }
            } catch (IllegalAccessException e) {
                continue;
            }
            parseCondition(sb,fieldName,fieldValue.toString());
        }
        return sb.toString();
    }

    private void parseCondition(StringBuilder sb,String fieldName,String fieldValue){
        sb.append(" and ");
        if(fieldName.contains("_in")){
           String key = fieldName.split("_in")[0];
           sb.append(key+" in ");
            String[] split = fieldValue.split(",");
            StringJoiner joiner = new StringJoiner(",","(",")");
            for (String s : split) {
                joiner.add("'"+s+"'");
            }
            sb.append(joiner);
        }else if(fieldName.contains("_gte")){
            String[] res = fieldName.split("_gte");
            sb.append(res[0]+" >= '"+fieldValue+"'");
        }else if(fieldName.contains("_lte")){
            String[] res = fieldName.split("_lte");
            sb.append(res[0]+"<= '"+fieldValue+"'");
        }else if(fieldValue.contains(",")){
            String[] split = fieldValue.split(",");
            sb.append(fieldName);
            sb.append(getOperator(split[0]));
            sb.append("'"+split[1]+"'");
        }else {
            sb.append(fieldName+" = '"+fieldValue+"'");
        }
    }

    private String getOperator(String operator){
        switch (operator){
            case "gte": return " >= ";
            case "lte": return " <= ";
            case "neq": return " <> ";
            default:return " = ";
        }
    }

    private CommunityUserAdditional updateAddition(CommunityUser user,UserInfoImportDto entry){
        CommunityUserAdditional additional = user.getAdditional();
        if(additional == null) {
            additional = new CommunityUserAdditional();
            additional.setId(user.getId());
            additional.setGender(StringUtils.isEmpty(entry.getSex()) ? null : entry.getSex());
            additional.setBirthday(StringUtils.isEmpty(entry.getBirthday()) ? null : DateFormatter.convertToDate(entry.getBirthday()));
            additional.setEducation(StringUtils.isEmpty(entry.getEducation()) ? null : entry.getEducation());
            additional.setEducationModified(new Date());
            additional.setProvince(StringUtils.isEmpty(entry.getProvince()) ? null : entry.getProvince());
            additional.setCity(StringUtils.isEmpty(entry.getCity()) ? null : entry.getCity());
            additional.setArea(StringUtils.isEmpty(entry.getArea()) ? null : entry.getArea());
        }else {
            //只更新没填写的字段
            if(StringUtils.isEmpty(additional.getGender())){
                additional.setGender(StringUtils.isEmpty(entry.getSex()) ? null : entry.getSex());
            }
            if(additional.getBirthday() == null){
                additional.setBirthday(StringUtils.isEmpty(entry.getBirthday()) ? null : DateFormatter.convertToDate(entry.getBirthday()));
            }
            if(StringUtils.isEmpty(additional.getEducation())){
                additional.setEducation(StringUtils.isEmpty(entry.getEducation()) ? null : entry.getEducation());
            }
            if(additional.getEducationModified()==null){
                additional.setEducationModified(new Date());
            }
            if(StringUtils.isEmpty(additional.getProvince())){
                additional.setProvince(StringUtils.isEmpty(entry.getProvince()) ? null : entry.getProvince());
            }
            if(StringUtils.isEmpty(additional.getCity())){
                additional.setCity(StringUtils.isEmpty(entry.getCity()) ? null : entry.getCity());
            }
            if(StringUtils.isEmpty(additional.getArea())){
                additional.setArea(StringUtils.isEmpty(entry.getArea()) ? null : entry.getArea());
            }
        }
        return additional;
    }

    /**
     * *检验用户状态
     * @param user
     */
    public void checkUserStatus(CommunityUser user) {
        if (user == null) throw new SurveyErrorException(SurveyErrorCode.USER_IS_DELETE);
        switch (user.getStatus()) {
            case 0:
                throw new SurveyErrorException(SurveyErrorCode.USER_IS_DELETE);
            case 2:
                throw new SurveyErrorException(SurveyErrorCode.USER_IS_BLACK);
            default:
        }
    }

}
